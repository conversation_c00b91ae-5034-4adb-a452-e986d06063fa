# Core module for Azure Landing Zone
# This module uses the provider configuration from the parent module

# Get the current client configuration from the AzureRM provider.

data "azurerm_client_config" "current" {}

# Local values for configuration
locals {
  # Custom landing zones configuration
  custom_landing_zones = {}

  # Identity resources configuration
  configure_identity_resources = {
    settings = {
      identity = {
        enabled = true
        config = {
          enable_deny_public_ip             = true
          enable_deny_rdp_from_internet     = true
          enable_deny_subnet_without_nsg    = true
          enable_deploy_azure_backup_on_vms = true
        }
      }
    }
  }
}

# Declare the Azure landing zones Terraform module
# and provide the core configuration for management groups and policies only.

module "alz" {
  source  = "Azure/caf-enterprise-scale/azurerm"
  version = "6.2.0" # change this to your desired version, https://www.terraform.io/language/expressions/version-constraints

  providers = {
    azurerm              = azurerm
    azurerm.connectivity = azurerm
    azurerm.management   = azurerm
  }
  default_location = var.primary_location

  # Base module configuration settings
  root_parent_id = data.azurerm_client_config.current.tenant_id
  root_id        = var.root_id
  root_name      = var.root_name
  library_path   = "${path.module}/lib"

  # Enable creation of the core management group hierarchy
  # and additional custom_landing_zones
  deploy_core_landing_zones = true
  custom_landing_zones      = local.custom_landing_zones

  # Configuration settings for identity resources is
  # bundled with core as no resources are actually created
  # for the identity subscription
  deploy_identity_resources    = true
  configure_identity_resources = local.configure_identity_resources
  subscription_id_identity     = var.subscription_id_identity

  # Disable connectivity and management resource deployment
  # These will be handled by separate modules
  deploy_connectivity_resources = false
  deploy_management_resources   = false

  # Disable custom role definitions due to insufficient permissions
  disable_telemetry = true

  # Map subscriptions to management groups without deploying resources
  subscription_id_connectivity = var.subscription_id_connectivity
  subscription_id_management   = var.subscription_id_management

  # Disable the problematic policy set definition by excluding it
  disable_base_module_tags = true

}
